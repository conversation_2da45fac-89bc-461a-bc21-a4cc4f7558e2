import type {
  GetFileResponse,
  GetFileNodesResponse,
  SimplifiedDesign,
  SimplifiedNode,
  BoundingBox
} from "./types.js";
import { removeEmptyKeys, parsePaint, isVisible, hasValue } from "./utils.js";

/**
 * 解析 Figma API 响应
 */
export function parseFigmaResponse(data: GetFileResponse | GetFileNodesResponse): SimplifiedDesign {
  const aggregatedComponents: Record<string, any> = {};
  const aggregatedComponentSets: Record<string, any> = {};
  let nodesToParse: Array<any>;

  if ("nodes" in data) {
    // GetFileNodesResponse
    const nodeResponses = Object.values(data.nodes);
    nodeResponses.forEach((nodeResponse) => {
      if (nodeResponse.components) {
        Object.assign(aggregatedComponents, nodeResponse.components);
      }
      if (nodeResponse.componentSets) {
        Object.assign(aggregatedComponentSets, nodeResponse.componentSets);
      }
    });
    nodesToParse = nodeResponses.map(response => response.document);
  } else {
    // GetFileResponse
    if (data.components) {
      Object.assign(aggregatedComponents, data.components);
    }
    if (data.componentSets) {
      Object.assign(aggregatedComponentSets, data.componentSets);
    }
    nodesToParse = [data.document];
  }

  const simplifiedNodes = nodesToParse.map(node => parseNode(node)).filter(Boolean) as SimplifiedNode[];

  return {
    name: data.name,
    lastModified: data.lastModified,
    thumbnailUrl: data.thumbnailUrl,
    nodes: simplifiedNodes,
    components: aggregatedComponents,
    componentSets: aggregatedComponentSets,
    globalVars: {
      styles: {} // TODO: 解析样式
    }
  };
}

/**
 * 解析单个节点
 */
function parseNode(node: any): SimplifiedNode | null {
  if (!node || !isVisible(node)) {
    return null;
  }

  const simplifiedNode: SimplifiedNode = {
    id: node.id,
    name: node.name,
    type: node.type
  };

  // 解析边界框
  if (node.absoluteBoundingBox) {
    simplifiedNode.boundingBox = {
      x: node.absoluteBoundingBox.x,
      y: node.absoluteBoundingBox.y,
      width: node.absoluteBoundingBox.width,
      height: node.absoluteBoundingBox.height
    };
  }

  // 解析文本
  if (node.characters) {
    simplifiedNode.text = node.characters;
  }

  // 解析填充
  if (node.fills && node.fills.length > 0) {
    const fills = node.fills.map(parsePaint).filter(Boolean);
    if (fills.length > 0) {
      simplifiedNode.fills = JSON.stringify(fills);
    }
  }

  // 解析描边
  if (node.strokes && node.strokes.length > 0) {
    const strokes = node.strokes.map(parsePaint).filter(Boolean);
    if (strokes.length > 0) {
      simplifiedNode.strokes = JSON.stringify(strokes);
    }
  }

  // 解析透明度
  if (hasValue(node.opacity) && node.opacity !== 1) {
    simplifiedNode.opacity = node.opacity;
  }

  // 解析圆角
  if (hasValue(node.cornerRadius)) {
    simplifiedNode.borderRadius = String(node.cornerRadius);
  } else if (node.rectangleCornerRadii) {
    simplifiedNode.borderRadius = node.rectangleCornerRadii.join(',');
  }

  // 解析组件信息
  if (node.componentId) {
    simplifiedNode.componentId = node.componentId;
  }

  if (node.componentProperties) {
    simplifiedNode.componentProperties = Object.entries(node.componentProperties).map(([name, prop]: [string, any]) => ({
      name,
      value: prop.value,
      type: prop.type
    }));
  }

  // 解析子节点
  if (node.children && node.children.length > 0) {
    const children = node.children.map(parseNode).filter(Boolean) as SimplifiedNode[];
    if (children.length > 0) {
      simplifiedNode.children = children;
    }
  }

  return removeEmptyKeys(simplifiedNode) as SimplifiedNode;
}

/**
 * 构建简化的布局信息
 */
export function buildSimplifiedLayout(node: any): any {
  const layout: any = {};

  if (node.layoutMode) {
    layout.layoutMode = node.layoutMode;
  }

  if (node.primaryAxisSizingMode) {
    layout.primaryAxisSizingMode = node.primaryAxisSizingMode;
  }

  if (node.counterAxisSizingMode) {
    layout.counterAxisSizingMode = node.counterAxisSizingMode;
  }

  if (node.primaryAxisAlignItems) {
    layout.primaryAxisAlignItems = node.primaryAxisAlignItems;
  }

  if (node.counterAxisAlignItems) {
    layout.counterAxisAlignItems = node.counterAxisAlignItems;
  }

  if (node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom) {
    layout.padding = {
      left: node.paddingLeft || 0,
      right: node.paddingRight || 0,
      top: node.paddingTop || 0,
      bottom: node.paddingBottom || 0
    };
  }

  if (node.itemSpacing) {
    layout.itemSpacing = node.itemSpacing;
  }

  return Object.keys(layout).length > 0 ? layout : null;
}
