/**
 * Style transformation utilities
 * Simplified version from Figma-Context-MCP
 */

import type { SimplifiedStroke } from "../types.js";
import { parsePaint } from "../common.js";

/**
 * Build simplified stroke information from Figma node
 */
export function buildSimplifiedStrokes(node: any): SimplifiedStroke {
  const stroke: SimplifiedStroke = {
    colors: [],
    weight: 0,
    align: "INSIDE"
  };

  // Stroke colors
  if (node.strokes && Array.isArray(node.strokes) && node.strokes.length > 0) {
    stroke.colors = node.strokes.map(parsePaint);
  }

  // Stroke weight
  if (node.strokeWeight && typeof node.strokeWeight === "number") {
    stroke.weight = node.strokeWeight;
  }

  // Stroke alignment
  if (node.strokeAlign) {
    stroke.align = node.strokeAlign;
  }

  // Individual stroke weights (for rectangles)
  if (node.individualStrokeWeights) {
    stroke.individualWeights = {
      top: node.individualStrokeWeights.top || 0,
      right: node.individualStrokeWeights.right || 0,
      bottom: node.individualStrokeWeights.bottom || 0,
      left: node.individualStrokeWeights.left || 0
    };
  }

  return stroke;
}
