import fs from "fs";
import path from "path";

/**
 * 带重试的 fetch 函数
 */
export async function fetchWithRetry<T>(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json() as T;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (i < maxRetries) {
        console.log(`Request failed, retrying in ${delay}ms... (${i + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // 指数退避
      }
    }
  }

  throw lastError!;
}

/**
 * 下载 Figma 图片
 */
export async function downloadFigmaImage(
  fileName: string,
  localPath: string,
  imageUrl: string
): Promise<string> {
  try {
    // 确保目录存在
    if (!fs.existsSync(localPath)) {
      fs.mkdirSync(localPath, { recursive: true });
    }

    const filePath = path.join(localPath, fileName);
    
    // 下载图片
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    fs.writeFileSync(filePath, Buffer.from(buffer));

    console.log(`Downloaded: ${fileName}`);
    return fileName;
  } catch (error) {
    console.error(`Failed to download ${fileName}:`, error);
    return "";
  }
}

/**
 * 移除对象中的空键
 */
export function removeEmptyKeys(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(removeEmptyKeys).filter(item => item !== null && item !== undefined);
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== null && value !== undefined && value !== '') {
        const cleanedValue = removeEmptyKeys(value);
        if (cleanedValue !== null && cleanedValue !== undefined && cleanedValue !== '') {
          result[key] = cleanedValue;
        }
      }
    }
    return Object.keys(result).length > 0 ? result : undefined;
  }

  return obj;
}

/**
 * 生成变量 ID
 */
export function generateVarId(prefix: string, id: string): string {
  return `${prefix}_${id.replace(/[^a-zA-Z0-9]/g, '_')}`;
}

/**
 * 解析颜色
 */
export function parsePaint(paint: any): any {
  if (!paint) return null;

  const result: any = {
    type: paint.type
  };

  if (paint.color) {
    const { r, g, b, a = 1 } = paint.color;
    result.rgba = `rgba(${Math.round(r * 255)}, ${Math.round(g * 255)}, ${Math.round(b * 255)}, ${a})`;
    result.hex = `#${Math.round(r * 255).toString(16).padStart(2, '0')}${Math.round(g * 255).toString(16).padStart(2, '0')}${Math.round(b * 255).toString(16).padStart(2, '0')}`;
  }

  if (paint.opacity !== undefined) {
    result.opacity = paint.opacity;
  }

  if (paint.imageRef) {
    result.imageRef = paint.imageRef;
  }

  if (paint.scaleMode) {
    result.scaleMode = paint.scaleMode;
  }

  return result;
}

/**
 * 检查节点是否可见
 */
export function isVisible(node: any): boolean {
  return node.visible !== false && (node.opacity === undefined || node.opacity > 0);
}

/**
 * 检查值是否存在
 */
export function hasValue(value: any): boolean {
  return value !== null && value !== undefined && value !== '';
}

/**
 * 检查是否为矩形圆角半径
 */
export function isRectangleCornerRadii(value: any): boolean {
  return Array.isArray(value) && value.length === 4 && value.every(v => typeof v === 'number');
}

/**
 * 检查值是否为真
 */
export function isTruthy(value: any): boolean {
  return !!value;
}

export type StyleId = string;
