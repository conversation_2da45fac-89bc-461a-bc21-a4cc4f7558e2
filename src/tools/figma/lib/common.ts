/**
 * Common utility functions
 * Copied and adapted from Figma-Context-MCP
 */

import type { Paint } from "@figma/rest-api-spec";
import type { SimplifiedFill, ColorValue, StyleId } from "./types.js";

/**
 * Remove empty keys from an object
 */
export function removeEmptyKeys<T extends Record<string, any>>(obj: T): T {
  const result = {} as T;
  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && value !== undefined && value !== "" && 
        !(Array.isArray(value) && value.length === 0) &&
        !(typeof value === "object" && Object.keys(value).length === 0)) {
      result[key as keyof T] = value;
    }
  }
  return result;
}

/**
 * Generate a unique variable ID
 */
export function generateVarId(prefix: string): StyleId {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * Convert Figma color to hex and opacity
 */
export function convertColor(color: { r: number; g: number; b: number }, opacity: number = 1): ColorValue {
  const r = Math.round(color.r * 255);
  const g = Math.round(color.g * 255);
  const b = Math.round(color.b * 255);
  const hex = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  return { hex, opacity };
}

/**
 * Format RGBA color string
 */
export function formatRGBAColor(color: { r: number; g: number; b: number }, opacity: number): string {
  const r = Math.round(color.r * 255);
  const g = Math.round(color.g * 255);
  const b = Math.round(color.b * 255);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

/**
 * Convert a Figma paint (solid, image, gradient) to a SimplifiedFill
 */
export function parsePaint(raw: Paint): SimplifiedFill {
  if (raw.type === "IMAGE") {
    console.log("Found IMAGE fill with imageRef:", raw.imageRef);
    return {
      type: "IMAGE",
      imageRef: raw.imageRef,
      scaleMode: raw.scaleMode,
    };
  } else if (raw.type === "SOLID") {
    // treat as SOLID
    const { hex, opacity } = convertColor(raw.color!, raw.opacity);
    if (opacity === 1) {
      return hex as SimplifiedFill;
    } else {
      return formatRGBAColor(raw.color!, opacity) as SimplifiedFill;
    }
  } else if (
    ["GRADIENT_LINEAR", "GRADIENT_RADIAL", "GRADIENT_ANGULAR", "GRADIENT_DIAMOND"].includes(
      raw.type,
    )
  ) {
    // treat as GRADIENT_LINEAR
    return {
      type: raw.type,
      gradientHandlePositions: (raw as any).gradientHandlePositions,
      gradientStops: (raw as any).gradientStops?.map(({ position, color }: any) => ({
        position,
        color: convertColor(color),
      })) || [],
    };
  } else {
    throw new Error(`Unknown paint type: ${raw.type}`);
  }
}

/**
 * Check if an element is visible
 */
export function isVisible(element: { visible?: boolean }): boolean {
  return element.visible ?? true;
}
