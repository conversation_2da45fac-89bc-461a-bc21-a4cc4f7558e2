/**
 * Sanitization utilities for Figma components
 * Copied from Figma-Context-MCP
 */

import type { Component, ComponentSet } from "@figma/rest-api-spec";
import type { SimplifiedComponentDefinition, SimplifiedComponentSetDefinition } from "./types.js";

/**
 * Sanitize components data
 */
export function sanitizeComponents(components: Record<string, Component>): Record<string, SimplifiedComponentDefinition> {
  const sanitized: Record<string, SimplifiedComponentDefinition> = {};
  
  for (const [id, component] of Object.entries(components)) {
    sanitized[id] = {
      id: component.key,
      name: component.name,
      description: component.description || undefined,
    };
  }
  
  return sanitized;
}

/**
 * Sanitize component sets data
 */
export function sanitizeComponentSets(componentSets: Record<string, ComponentSet>): Record<string, SimplifiedComponentSetDefinition> {
  const sanitized: Record<string, SimplifiedComponentSetDefinition> = {};
  
  for (const [id, componentSet] of Object.entries(componentSets)) {
    sanitized[id] = {
      id: componentSet.key,
      name: componentSet.name,
      description: componentSet.description || undefined,
    };
  }
  
  return sanitized;
}
