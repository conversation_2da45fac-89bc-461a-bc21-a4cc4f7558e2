/**
 * Identity and validation utility functions
 * Copied from Figma-Context-MCP
 */

/**
 * Check if a value exists and is not null/undefined
 */
export function hasValue<T, K extends keyof T>(
  key: K,
  obj: T,
  validator?: (value: T[K]) => boolean
): obj is T & Required<Pick<T, K>> {
  const value = obj[key];
  if (value === null || value === undefined) {
    return false;
  }
  return validator ? validator(value) : true;
}

/**
 * Check if a value is truthy
 */
export function isTruthy<T>(value: T): value is NonNullable<T> {
  return Boolean(value);
}

/**
 * Check if value is a valid rectangle corner radii array
 */
export function isRectangleCornerRadii(value: any): value is number[] {
  return Array.isArray(value) && value.length === 4 && value.every(v => typeof v === 'number');
}

/**
 * Check if an element is visible
 */
export function isVisible(element: { visible?: boolean }): boolean {
  return element.visible ?? true;
}
