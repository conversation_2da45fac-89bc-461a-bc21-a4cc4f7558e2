#!/usr/bin/env node

import { config } from "dotenv";
import { resolve } from "path";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { registerTools } from "./tools/index.js";

// Load .env from the current working directory
config({ path: resolve(process.cwd(), ".env") });

// 创建 MCP 服务器
const server = new McpServer({
  name: "shell-executor",
  version: "1.0.0"
});

// 注册所有工具
registerTools(server);

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);

  // 服务器启动完成，不输出日志
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
main().catch((error) => {
  console.error("❌ Failed to start server:", error);
  process.exit(1);
});
