import { z } from "zod";
import { FigmaService } from "../lib/figma-service.js";
export const downloadFigmaImagesTool = {
    name: "download_figma_images",
    config: {
        title: "Download Figma Images",
        description: "Download SVG and PNG images used in a Figma file based on the IDs of image or icon nodes",
        inputSchema: {
            fileKey: z.string().describe("The key of the Figma file containing the node"),
            nodes: z
                .object({
                nodeId: z
                    .string()
                    .describe("The ID of the Figma image node to fetch, formatted as 1234:5678"),
                imageRef: z
                    .string()
                    .optional()
                    .describe("If a node has an imageRef fill, you must include this variable. Leave blank when downloading Vector SVG images."),
                fileName: z.string().describe("The local name for saving the fetched file")
            })
                .array()
                .describe("The nodes to fetch as images"),
            pngScale: z
                .number()
                .positive()
                .optional()
                .default(2)
                .describe("Export scale for PNG images. Optional, defaults to 2 if not specified. Affects PNG images only."),
            localPath: z
                .string()
                .describe("The absolute path to the directory where images are stored in the project. If the directory does not exist, it will be created. The format of this path should respect the directory format of the operating system you are running on. Don't use any special character escaping in the path name either."),
            svgOptions: z
                .object({
                outlineText: z
                    .boolean()
                    .optional()
                    .default(true)
                    .describe("Whether to outline text in SVG exports. Default is true."),
                includeId: z
                    .boolean()
                    .optional()
                    .default(false)
                    .describe("Whether to include IDs in SVG exports. Default is false."),
                simplifyStroke: z
                    .boolean()
                    .optional()
                    .default(true)
                    .describe("Whether to simplify strokes in SVG exports. Default is true.")
            })
                .optional()
                .default({})
                .describe("Options for SVG export")
        }
    },
    handler: async (args) => {
        const { fileKey, nodes, localPath, svgOptions = {}, pngScale = 2 } = args;
        try {
            // 从环境变量获取认证信息
            const authOptions = {
                figmaApiKey: process.env.FIGMA_API_KEY || "",
                figmaOAuthToken: process.env.FIGMA_OAUTH_TOKEN || "",
                useOAuth: !!process.env.FIGMA_OAUTH_TOKEN
            };
            // 检查是否有认证信息
            if (!authOptions.figmaApiKey && !authOptions.figmaOAuthToken) {
                return {
                    isError: true,
                    content: [{
                            type: "text",
                            text: "Error: No Figma authentication found. Please set FIGMA_API_KEY or FIGMA_OAUTH_TOKEN environment variable."
                        }]
                };
            }
            const figmaService = new FigmaService(authOptions);
            // 分离有 imageRef 的节点（用于下载填充图片）和没有的节点（用于渲染）
            const imageFills = nodes.filter(({ imageRef }) => !!imageRef);
            const renderRequests = nodes
                .filter(({ imageRef }) => !imageRef)
                .map(({ nodeId, fileName }) => ({
                nodeId,
                fileName,
                fileType: fileName.endsWith(".svg") ? "svg" : "png"
            }));
            // 下载填充图片
            const fillDownloads = figmaService.getImageFills(fileKey, imageFills, localPath);
            // 渲染并下载图片
            const renderDownloads = figmaService.getImages(fileKey, renderRequests, localPath, pngScale, {
                outlineText: svgOptions.outlineText ?? true,
                includeId: svgOptions.includeId ?? false,
                simplifyStroke: svgOptions.simplifyStroke ?? true
            });
            const downloads = await Promise.all([fillDownloads, renderDownloads]).then(([f, r]) => [
                ...f,
                ...r
            ]);
            // 检查是否有下载失败的
            const saveSuccess = !downloads.find((success) => !success);
            return {
                content: [
                    {
                        type: "text",
                        text: saveSuccess
                            ? `Success, ${downloads.length} images downloaded: ${downloads.join(", ")}`
                            : "Failed"
                    }
                ]
            };
        }
        catch (error) {
            console.error(`Error downloading images from file ${fileKey}:`, error);
            return {
                isError: true,
                content: [{ type: "text", text: `Error downloading images: ${error}` }]
            };
        }
    }
};
//# sourceMappingURL=index.js.map