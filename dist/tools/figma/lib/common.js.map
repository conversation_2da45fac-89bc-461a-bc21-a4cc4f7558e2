{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/lib/common.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAKH;;GAEG;AACH,MAAM,UAAU,eAAe,CAAgC,GAAM;IACnE,MAAM,MAAM,GAAG,EAAO,CAAC;IACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE;YACrD,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;YAC7C,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YACpE,MAAM,CAAC,GAAc,CAAC,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,MAAc;IAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvD,OAAO,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAA0C,EAAE,UAAkB,CAAC;IAC1F,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;IACtH,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,KAA0C,EAAE,OAAe;IACzF,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,OAAO,GAAG,CAAC;AAC9C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,GAAU;IACnC,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACzB,OAAO;YACL,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,GAAG,CAAC,QAAQ;YACtB,SAAS,EAAE,GAAG,CAAC,SAAS;SACzB,CAAC;IACJ,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAChC,iBAAiB;QACjB,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,GAAG,CAAC,KAAM,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/D,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO,GAAqB,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,OAAO,eAAe,CAAC,GAAG,CAAC,KAAM,EAAE,OAAO,CAAmB,CAAC;QAChE,CAAC;IACH,CAAC;SAAM,IACL,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CACrF,GAAG,CAAC,IAAI,CACT,EACD,CAAC;QACD,2BAA2B;QAC3B,OAAO;YACL,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,uBAAuB,EAAG,GAAW,CAAC,uBAAuB;YAC7D,aAAa,EAAG,GAAW,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAO,EAAE,EAAE,CAAC,CAAC;gBAC5E,QAAQ;gBACR,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC;aAC3B,CAAC,CAAC,IAAI,EAAE;SACV,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,OAA8B;IACtD,OAAO,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;AACjC,CAAC"}