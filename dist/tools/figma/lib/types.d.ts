import type { Paint, Vector, ComponentPropertyType } from "@figma/rest-api-spec";
export type FigmaAuthOptions = {
    figmaApiKey: string;
    figmaOAuthToken: string;
    useOAuth: boolean;
};
export interface BoundingBox {
    x: number;
    y: number;
    width: number;
    height: number;
}
export type CSSRGBAColor = `rgba(${number}, ${number}, ${number}, ${number})`;
export type CSSHexColor = `#${string}`;
export interface ColorValue {
    hex: string;
    opacity: number;
}
export type SimplifiedFill = {
    type?: Paint["type"];
    hex?: string;
    rgba?: string;
    opacity?: number;
    imageRef?: string;
    scaleMode?: string;
    gradientHandlePositions?: Vector[];
    gradientStops?: {
        position: number;
        color: ColorValue | string;
    }[];
} | CSSRGBAColor | CSSHexColor;
export interface ComponentProperties {
    name: string;
    value: string;
    type: ComponentPropertyType;
}
export interface SimplifiedNode {
    id: string;
    name: string;
    type: string;
    boundingBox?: BoundingBox;
    text?: string;
    textStyle?: string;
    fills?: string;
    styles?: string;
    strokes?: string;
    effects?: string;
    opacity?: number;
    borderRadius?: string;
    layout?: string;
    componentId?: string;
    componentProperties?: ComponentProperties[];
    children?: SimplifiedNode[];
}
export interface SimplifiedDesign {
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    nodes: SimplifiedNode[];
    components: Record<string, SimplifiedComponentDefinition>;
    componentSets: Record<string, SimplifiedComponentSetDefinition>;
    globalVars: GlobalVars;
}
export type TextStyle = Partial<{
    fontFamily: string;
    fontWeight: number;
    fontSize: number;
    lineHeight: string;
    letterSpacing: string;
    textCase: string;
    textAlignHorizontal: string;
    textAlignVertical: string;
}>;
export type StrokeWeights = {
    top: number;
    right: number;
    bottom: number;
    left: number;
};
export type StyleId = string;
export type SimplifiedLayout = Record<string, any>;
export type SimplifiedStroke = Record<string, any>;
export type SimplifiedEffects = Record<string, any>;
export type StyleTypes = TextStyle | SimplifiedFill[] | SimplifiedLayout | SimplifiedStroke | SimplifiedEffects | string;
export type GlobalVars = {
    styles: Record<StyleId, StyleTypes>;
};
export interface SimplifiedComponentDefinition {
    id: string;
    name: string;
    description?: string;
}
export interface SimplifiedComponentSetDefinition {
    id: string;
    name: string;
    description?: string;
}
export type FetchImageParams = {
    nodeId: string;
    fileName: string;
    fileType: "png" | "svg";
};
export type FetchImageFillParams = Omit<FetchImageParams, "fileType"> & {
    imageRef: string;
};
export interface SvgOptions {
    outlineText: boolean;
    includeId: boolean;
    simplifyStroke: boolean;
}
export interface GetFileResponse {
    document: any;
    components: Record<string, any>;
    componentSets: Record<string, any>;
    schemaVersion: number;
    styles: Record<string, any>;
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    version: string;
    role: string;
    editorType: string;
    linkAccess: string;
}
export interface GetFileNodesResponse {
    name: string;
    lastModified: string;
    thumbnailUrl: string;
    version: string;
    role: string;
    editorType: string;
    linkAccess: string;
    nodes: Record<string, {
        document: any;
        components?: Record<string, any>;
        componentSets?: Record<string, any>;
        schemaVersion: number;
        styles?: Record<string, any>;
    }>;
}
export interface GetImagesResponse {
    err?: string;
    images?: Record<string, string>;
    status?: number;
}
export interface GetImageFillsResponse {
    meta: {
        images?: Record<string, string>;
    };
}
//# sourceMappingURL=types.d.ts.map