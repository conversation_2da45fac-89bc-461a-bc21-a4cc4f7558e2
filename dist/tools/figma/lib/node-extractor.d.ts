/**
 * Node extraction utilities for converting hierarchical nodes to downloadable image arrays
 */
import type { SimplifiedDesign } from "./types.js";
export interface DownloadableNode {
    nodeId: string;
    fileName: string;
    imageRef?: string;
    nodeType: string;
    nodeName: string;
}
/**
 * Extract downloadable nodes from a simplified design
 */
export declare function extractDownloadableNodes(design: SimplifiedDesign): DownloadableNode[];
/**
 * Filter nodes by type
 */
export declare function filterNodesByType(nodes: DownloadableNode[], types: string[]): DownloadableNode[];
/**
 * Filter nodes that have image references
 */
export declare function filterNodesWithImages(nodes: DownloadableNode[]): DownloadableNode[];
/**
 * Filter nodes that are vectors (SVG)
 */
export declare function filterVectorNodes(nodes: DownloadableNode[]): DownloadableNode[];
//# sourceMappingURL=node-extractor.d.ts.map