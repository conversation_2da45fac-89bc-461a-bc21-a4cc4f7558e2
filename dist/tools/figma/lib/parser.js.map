{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/lib/parser.ts"], "names": [], "mappings": "AAWA,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACpF,OAAO,EAAY,QAAQ,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AAC3E,OAAO,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAC9E,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;AACjE,OAAO,EAAE,sBAAsB,EAAE,MAAM,2BAA2B,CAAC;AAEnE;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAA4C;IAC7E,MAAM,oBAAoB,GAA8B,EAAE,CAAC;IAC3D,MAAM,uBAAuB,GAAiC,EAAE,CAAC;IACjE,IAAI,YAAsC,CAAC;IAE3C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,uBAAuB;QACvB,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;YACrC,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC/B,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,YAAY,CAAC,aAAa,CAAC,CAAC;YACrE,CAAC;QACH,CAAC,CAAC,CAAC;QACH,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;SAAM,CAAC;QACN,kBAAkB;QAClB,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;IACrE,MAAM,sBAAsB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,CAAC;IAE9E,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAElD,IAAI,UAAU,GAAe;QAC3B,MAAM,EAAE,EAAE;KACX,CAAC;IAEF,MAAM,eAAe,GAAqB,YAAY;SACnD,MAAM,CAAC,SAAS,CAAC;SACjB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACpC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,CAAC;IAE5D,MAAM,gBAAgB,GAAqB;QACzC,IAAI;QACJ,YAAY;QACZ,YAAY,EAAE,YAAY,IAAI,EAAE;QAChC,KAAK,EAAE,eAAe;QACtB,UAAU,EAAE,mBAAmB;QAC/B,aAAa,EAAE,sBAAsB;QACrC,UAAU;KACX,CAAC;IAEF,OAAO,eAAe,CAAC,gBAAgB,CAAC,CAAC;AAC3C,CAAC;AAED,qDAAqD;AACrD,SAAS,eAAe,CAAC,UAAsB,EAAE,KAAU,EAAE,MAAc;IACzE,yCAAyC;IACzC,MAAM,CAAC,aAAa,CAAC,GACnB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CACpC,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAChF,IAAI,EAAE,CAAC;IAEV,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,aAAwB,CAAC;IAClC,CAAC;IAED,4CAA4C;IAC5C,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;IACpC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACjC,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAChB,UAAsB,EACtB,CAAM,EACN,MAAY;IAEZ,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAE7B,MAAM,UAAU,GAAmB;QACjC,EAAE;QACF,IAAI;QACJ,IAAI;KACL,CAAC;IAEF,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;QACxB,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YAClB,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC;QACzC,CAAC;QAED,sDAAsD;QACtD,IAAI,CAAC,CAAC,mBAAmB,EAAE,CAAC;YAC1B,UAAU,CAAC,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC,GAAG,CAC9E,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAgB,EAAE,EAAE,CAAC,CAAC;gBAC3C,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;gBACvB,IAAI;aACL,CAAC,CACH,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO;IACP,IAAI,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QACtB,MAAM,SAAS,GAAc;YAC3B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,UAAU,EACR,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,QAAQ;gBAClC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,QAAQ,IAAI;gBAC5C,CAAC,CAAC,SAAS;YACf,aAAa,EACX,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ;gBAChE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG;gBACpD,CAAC,CAAC,SAAS;YACf,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,mBAAmB,EAAE,KAAK,CAAC,mBAAmB;YAC9C,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;SAC3C,CAAC;QACF,UAAU,CAAC,SAAS,GAAG,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,kBAAkB;IAClB,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACxD,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtC,UAAU,CAAC,KAAK,GAAG,eAAe,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAC1B,UAAU,CAAC,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;QAChC,UAAU,CAAC,OAAO,GAAG,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IAED,iBAAiB;IACjB,MAAM,MAAM,GAAG,qBAAqB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAChD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnC,UAAU,CAAC,MAAM,GAAG,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpE,CAAC;IAED,wCAAwC;IACxC,IAAI,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;QAC3C,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC;IACjC,CAAC;IAED,UAAU;IACV,IAAI,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;QAClE,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;IACjC,CAAC;IAED,IAAI,CAAC,CAAC,YAAY,IAAI,OAAO,CAAC,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;QACzD,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,YAAY,IAAI,CAAC;IAClD,CAAC;IACD,IAAI,CAAC,CAAC,oBAAoB,IAAI,sBAAsB,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC7E,UAAU,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1J,CAAC;IAED,mCAAmC;IACnC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,CAAC,CAAC,QAAQ;aACxB,MAAM,CAAC,SAAS,CAAC;aACjB,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;aACpD,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC,CAAC;QACjE,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACjC,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtB,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;IAChC,CAAC;IAED,OAAO,UAAU,CAAC;AACpB,CAAC"}