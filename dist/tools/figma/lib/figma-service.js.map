{"version": 3, "file": "figma-service.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/lib/figma-service.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,SAAS,CAAC;AAY3B,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEhE,MAAM,OAAO,YAAY;IACN,MAAM,CAAS;IACf,UAAU,CAAS;IACnB,QAAQ,CAAU;IAClB,OAAO,GAAG,0BAA0B,CAAC;IAEtD,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAoB;QACtE,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,eAAe,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,OAAO,CAAI,QAAgB;QACvC,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,CAAC,CAAC;YAElD,kDAAkD;YAClD,MAAM,OAAO,GAA2B,EAAE,CAAC;YAE3C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,oDAAoD;gBACpD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAC3D,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,sDAAsD;gBACtD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;gBAC9D,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;YACzC,CAAC;YAED,OAAO,MAAM,cAAc,CAAI,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE,EAAE;gBAC3D,OAAO;aACR,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,wCAAwC,KAAK,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,OAAe,EACf,KAA6B,EAC7B,SAAiB;QAEjB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAElC,IAAI,QAAQ,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAG,UAAU,OAAO,SAAS,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAwB,QAAQ,CAAC,CAAC;QACjE,MAAM,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAClC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,SAAS,CACb,OAAe,EACf,KAAyB,EACzB,SAAiB,EACjB,QAAgB,EAChB,UAAsB;QAEtB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9F,MAAM,QAAQ,GACZ,MAAM,CAAC,MAAM,GAAG,CAAC;YACf,CAAC,CAAC,IAAI,CAAC,OAAO,CACV,WAAW,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,QAAQ,EAAE,CAC1E,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC;YACrC,CAAC,CAAE,EAAkC,CAAC;QAE1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9F,MAAM,SAAS,GAAG;YAChB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACzB,YAAY;YACZ,oBAAoB,UAAU,CAAC,WAAW,EAAE;YAC5C,kBAAkB,UAAU,CAAC,SAAS,EAAE;YACxC,uBAAuB,UAAU,CAAC,cAAc,EAAE;SACnD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEZ,MAAM,QAAQ,GACZ,MAAM,CAAC,MAAM,GAAG,CAAC;YACf,CAAC,CAAC,IAAI,CAAC,OAAO,CAAoB,WAAW,OAAO,IAAI,SAAS,EAAE,CAAC,CAAC,IAAI,CACrE,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,CAC5B;YACH,CAAC,CAAE,EAAkC,CAAC;QAE1C,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzF,MAAM,SAAS,GAAG,KAAK;aACpB,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE;YAC5B,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC/B,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC3D,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAe,EAAE,KAAqB;QAClD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,UAAU,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,OAAO,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;YAChF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAkB,QAAQ,CAAC,CAAC;YAC/D,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAC5B,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;YAC3D,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAe,EAAE,MAAc,EAAE,KAAqB;QAClE,MAAM,QAAQ,GAAG,UAAU,OAAO,cAAc,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAuB,QAAQ,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC1C,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;QAC3D,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAIO,SAAS,CAAC,IAAY,EAAE,KAAU;QACxC,IAAI,CAAC;YACH,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;gBAAE,OAAO;YAEnD,MAAM,OAAO,GAAG,MAAM,CAAC;YAEvB,IAAI,CAAC;gBACH,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;gBAC5C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC;YACD,EAAE,CAAC,aAAa,CAAC,GAAG,OAAO,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF"}