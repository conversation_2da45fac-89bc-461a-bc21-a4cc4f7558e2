import { removeEmpt<PERSON><PERSON><PERSON><PERSON>, parsePaint, isVisible, generateVarId } from "./common.js";
import { isTruthy, isRectangleCornerRadii } from "./identity.js";
import { sanitizeComponents, sanitizeComponentSets } from "./sanitization.js";
import { buildSimplifiedLayout } from "./transformers/layout.js";
import { buildSimplifiedStrokes } from "./transformers/style.js";
import { buildSimplifiedEffects } from "./transformers/effects.js";
/**
 * 解析 Figma API 响应
 */
export function parseFigmaResponse(data) {
    const aggregatedComponents = {};
    const aggregatedComponentSets = {};
    let nodesToParse;
    if ("nodes" in data) {
        // GetFileNodesResponse
        const nodeResponses = Object.values(data.nodes);
        nodeResponses.forEach((nodeResponse) => {
            if (nodeResponse.components) {
                Object.assign(aggregatedComponents, nodeResponse.components);
            }
            if (nodeResponse.componentSets) {
                Object.assign(aggregatedComponentSets, nodeResponse.componentSets);
            }
        });
        nodesToParse = nodeResponses.map((n) => n.document);
    }
    else {
        // GetFileResponse
        Object.assign(aggregatedComponents, data.components);
        Object.assign(aggregatedComponentSets, data.componentSets);
        nodesToParse = data.document.children;
    }
    const sanitizedComponents = sanitizeComponents(aggregatedComponents);
    const sanitizedComponentSets = sanitizeComponentSets(aggregatedComponentSets);
    const { name, lastModified, thumbnailUrl } = data;
    let globalVars = {
        styles: {},
    };
    const simplifiedNodes = nodesToParse
        .filter(isVisible)
        .map((n) => parseNode(globalVars, n))
        .filter((child) => child !== null && child !== undefined);
    const simplifiedDesign = {
        name,
        lastModified,
        thumbnailUrl: thumbnailUrl || "",
        nodes: simplifiedNodes,
        components: sanitizedComponents,
        componentSets: sanitizedComponentSets,
        globalVars,
    };
    return removeEmptyKeys(simplifiedDesign);
}
// Helper function to find or create global variables
function findOrCreateVar(globalVars, value, prefix) {
    // Check if the same value already exists
    const [existingVarId] = Object.entries(globalVars.styles).find(([_, existingValue]) => JSON.stringify(existingValue) === JSON.stringify(value)) ?? [];
    if (existingVarId) {
        return existingVarId;
    }
    // Create a new variable if it doesn't exist
    const varId = generateVarId(prefix);
    globalVars.styles[varId] = value;
    return varId;
}
/**
 * 解析单个节点
 */
function parseNode(globalVars, n, parent) {
    const { id, name, type } = n;
    const simplified = {
        id,
        name,
        type,
    };
    if (type === "INSTANCE") {
        if (n.componentId) {
            simplified.componentId = n.componentId;
        }
        // Add specific properties for instances of components
        if (n.componentProperties) {
            simplified.componentProperties = Object.entries(n.componentProperties ?? {}).map(([name, { value, type }]) => ({
                name,
                value: value.toString(),
                type,
            }));
        }
    }
    // text
    if (n.style && Object.keys(n.style).length) {
        const style = n.style;
        const textStyle = {
            fontFamily: style.fontFamily,
            fontWeight: style.fontWeight,
            fontSize: style.fontSize,
            lineHeight: style.lineHeightPx && style.fontSize
                ? `${style.lineHeightPx / style.fontSize}em`
                : undefined,
            letterSpacing: style.letterSpacing && style.letterSpacing !== 0 && style.fontSize
                ? `${(style.letterSpacing / style.fontSize) * 100}%`
                : undefined,
            textCase: style.textCase,
            textAlignHorizontal: style.textAlignHorizontal,
            textAlignVertical: style.textAlignVertical,
        };
        simplified.textStyle = findOrCreateVar(globalVars, textStyle, "style");
    }
    // fills & strokes
    if (n.fills && Array.isArray(n.fills) && n.fills.length) {
        const fills = n.fills.map(parsePaint);
        simplified.fills = findOrCreateVar(globalVars, fills, "fill");
    }
    const strokes = buildSimplifiedStrokes(n);
    if (strokes.colors.length) {
        simplified.strokes = findOrCreateVar(globalVars, strokes, "stroke");
    }
    const effects = buildSimplifiedEffects(n);
    if (Object.keys(effects).length) {
        simplified.effects = findOrCreateVar(globalVars, effects, "effect");
    }
    // Process layout
    const layout = buildSimplifiedLayout(n, parent);
    if (Object.keys(layout).length > 1) {
        simplified.layout = findOrCreateVar(globalVars, layout, "layout");
    }
    // Keep other simple properties directly
    if (n.characters && isTruthy(n.characters)) {
        simplified.text = n.characters;
    }
    // opacity
    if (n.opacity && typeof n.opacity === "number" && n.opacity !== 1) {
        simplified.opacity = n.opacity;
    }
    if (n.cornerRadius && typeof n.cornerRadius === "number") {
        simplified.borderRadius = `${n.cornerRadius}px`;
    }
    if (n.rectangleCornerRadii && isRectangleCornerRadii(n.rectangleCornerRadii)) {
        simplified.borderRadius = `${n.rectangleCornerRadii[0]}px ${n.rectangleCornerRadii[1]}px ${n.rectangleCornerRadii[2]}px ${n.rectangleCornerRadii[3]}px`;
    }
    // Recursively process child nodes.
    if (n.children && n.children.length > 0) {
        const children = n.children
            .filter(isVisible)
            .map((child) => parseNode(globalVars, child, n))
            .filter((child) => child !== null && child !== undefined);
        if (children.length) {
            simplified.children = children;
        }
    }
    // Convert VECTOR to IMAGE-SVG
    if (type === "VECTOR") {
        simplified.type = "IMAGE-SVG";
    }
    return simplified;
}
//# sourceMappingURL=parser.js.map