{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/tools/figma/get_figma_data/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,IAAI,MAAM,SAAS,CAAC;AAC3B,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAEvD,OAAO,EAAE,wBAAwB,IAAI,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAEpF,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,IAAI,EAAE,gBAAgB;IACtB,MAAM,EAAE;QACN,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,+FAA+F;QAC5G,WAAW,EAAE;YACX,OAAO,EAAE,CAAC;iBACP,MAAM,EAAE;iBACR,QAAQ,CACP,8GAA8G,CAC/G;YACH,MAAM,EAAE,CAAC;iBACN,MAAM,EAAE;iBACR,QAAQ,EAAE;iBACV,QAAQ,CACP,oGAAoG,CACrG;YACH,KAAK,EAAE,CAAC;iBACL,MAAM,EAAE;iBACR,QAAQ,EAAE;iBACV,QAAQ,CACP,uHAAuH,CACxH;YACH,wBAAwB,EAAE,CAAC;iBACxB,OAAO,EAAE;iBACT,QAAQ,EAAE;iBACV,QAAQ,CACP,iGAAiG,CAClG;SACJ;KACF;IACD,OAAO,EAAE,KAAK,EAAE,IAAS,EAAE,EAAE;QAC3B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC;QAElE,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;YAEjD,cAAc;YACd,MAAM,WAAW,GAAqB;gBACpC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE;gBAC5C,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;gBACpD,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB;aAC1C,CAAC;YAEF,OAAO;YACP,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YACjE,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAElF,YAAY;YACZ,IAAI,CAAC,WAAW,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;gBAC7D,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAe;4BACrB,IAAI,EAAE,2GAA2G;yBAClH,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;YAEnD,OAAO,CAAC,GAAG,CACT,YACE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,cAAc,CAAC,CAAC,CAAC,YACnC,OAAO,MAAM,CAAC,CAAC,CAAC,QAAQ,MAAM,YAAY,CAAC,CAAC,CAAC,WAAW,IAAI,OAAO,EAAE,CACtE,CAAC;YAEF,IAAI,IAAI,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,QAAQ,EAAE,GAAG,IAAI,CAAC;YAEhD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YAChE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAE9C,IAAI,MAAW,CAAC;YAEhB,IAAI,wBAAwB,EAAE,CAAC;gBAC7B,aAAa;gBACb,MAAM,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAEvE,MAAM,GAAG;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,UAAU,EAAE,iBAAiB,CAAC,MAAM;oBACpC,UAAU,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;oBACnE,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM;iBACtE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,YAAY;gBACZ,MAAM,GAAG;oBACP,QAAQ;oBACR,KAAK;oBACL,UAAU;iBACX,CAAC;YACJ,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO;gBACL,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAe,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;aAC5D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/E,OAAO,CAAC,KAAK,CAAC,uBAAuB,OAAO,GAAG,EAAE,OAAO,CAAC,CAAC;YAC1D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAe,EAAE,IAAI,EAAE,wBAAwB,OAAO,EAAE,EAAE,CAAC;aAC9E,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC"}