import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
interface ToolConfig {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema?: any;
    };
    handler: (args: any) => Promise<any>;
}
declare const figmaTools: ToolConfig[];
/**
 * 注册所有 Figma 工具到 MCP 服务器
 */
export declare function registerFigmaTools(server: McpServer): void;
/**
 * 获取所有 Figma 工具的信息
 */
export declare function getFigmaToolsInfo(): {
    name: string;
    title: string;
    description: string;
}[];
export { figmaTools };
//# sourceMappingURL=index.d.ts.map