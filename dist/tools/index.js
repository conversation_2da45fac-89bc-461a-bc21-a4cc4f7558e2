import { registerLocalTools, getLocalToolsInfo, localTools } from "./local/index.js";
import { registerFigmaTools, getFigmaToolsInfo, figmaTools } from "./figma/index.js";
// 所有工具列表
const allTools = [
    ...localTools,
    ...figmaTools
];
/**
 * 注册所有工具到 MCP 服务器
 */
export function registerTools(server) {
    // 注册 Local 工具
    registerLocalTools(server);
    // 注册 Figma 工具
    registerFigmaTools(server);
    // 工具注册完成，不输出日志
}
/**
 * 获取所有工具的信息
 */
export function getToolsInfo() {
    return [
        ...getLocalToolsInfo(),
        ...getFigmaToolsInfo()
    ];
}
/**
 * 按类别获取工具信息
 */
export function getToolsByCategory() {
    return {
        local: getLocalToolsInfo(),
        figma: getFigmaToolsInfo()
    };
}
//# sourceMappingURL=index.js.map