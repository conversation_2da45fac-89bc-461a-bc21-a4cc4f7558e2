import { z } from "zod";
import { exec } from "child_process";
import { promisify } from "util";
const execAsync = promisify(exec);
export const executeShellTool = {
    name: "shell",
    config: {
        title: "执行本地脚本",
        description: "执行本地脚本并返回结果",
        inputSchema: {
            command: z.string().describe("The shell command to execute"),
            timeout: z.number().optional().describe("Timeout in seconds (default 20)")
        }
    },
    handler: async (args) => {
        const { command, timeout = 20 } = args;
        try {
            const { stdout, stderr } = await execAsync(command, {
                timeout: timeout * 1000
            });
            let output = '';
            if (stdout) {
                output += stdout;
            }
            if (stderr) {
                output += stderr;
            }
            return {
                content: [{
                        type: "text",
                        text: output
                    }]
            };
        }
        catch (error) {
            let errorOutput = '';
            if (error.stdout) {
                errorOutput += error.stdout;
            }
            if (error.stderr) {
                errorOutput += error.stderr;
            }
            if (!errorOutput && error.message) {
                errorOutput = error.message;
            }
            return {
                content: [{
                        type: "text",
                        text: errorOutput
                    }],
                isError: true
            };
        }
    }
};
//# sourceMappingURL=index.js.map