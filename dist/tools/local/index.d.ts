import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
declare const localTools: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: {};
    };
    handler: (args: any) => Promise<{
        content: {
            type: "text";
            text: string;
        }[];
    }>;
}[];
/**
 * 注册所有 Local 工具到 MCP 服务器
 */
export declare function registerLocalTools(server: McpServer): void;
/**
 * 获取所有 Local 工具的信息
 */
export declare function getLocalToolsInfo(): {
    name: string;
    title: string;
    description: string;
}[];
export { localTools };
//# sourceMappingURL=index.d.ts.map