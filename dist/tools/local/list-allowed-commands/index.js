import { SECURITY_CONFIG } from "../../../config/security.js";
export const listAllowedCommandsTool = {
    name: "list-allowed-commands",
    config: {
        title: "List Allowed Commands",
        description: "Show the list of allowed and forbidden commands",
        inputSchema: {}
    },
    handler: async (args) => {
        let output = `🔒 Security Configuration\n\n`;
        output += `✅ Allowed Commands:\n`;
        SECURITY_CONFIG.allowedCommands.forEach(cmd => {
            output += `  • ${cmd}\n`;
        });
        output += `\n❌ Forbidden Commands:\n`;
        SECURITY_CONFIG.forbiddenCommands.forEach(cmd => {
            output += `  • ${cmd}\n`;
        });
        output += `\n⚙️  Limits:\n`;
        output += `  • Max execution time: ${SECURITY_CONFIG.maxExecutionTime / 1000}s\n`;
        output += `  • Max output length: ${SECURITY_CONFIG.maxOutputLength} bytes\n`;
        return {
            content: [{
                    type: "text",
                    text: output
                }]
        };
    }
};
//# sourceMappingURL=index.js.map