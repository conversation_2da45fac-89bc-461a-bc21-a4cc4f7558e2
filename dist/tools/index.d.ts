import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
/**
 * 注册所有工具到 MCP 服务器
 */
export declare function registerTools(server: McpServer): void;
/**
 * 获取所有工具的信息
 */
export declare function getToolsInfo(): {
    name: string;
    title: string;
    description: string;
}[];
/**
 * 按类别获取工具信息
 */
export declare function getToolsByCategory(): {
    local: {
        name: string;
        title: string;
        description: string;
    }[];
    figma: {
        name: string;
        title: string;
        description: string;
    }[];
};
//# sourceMappingURL=index.d.ts.map